using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Extensions;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

[TestTimeout(TestTimeouts.Unit)]
[Trait("Category", TestCategories.Unit)]
public class RealTimeExecutionServiceTests : IDisposable
{
    private readonly Mock<ITickStreamService> _mockTickStreamService;
    private readonly Mock<IVWAPMonitorService> _mockVwapMonitorService;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IDatabase> _mockRedis;
    private readonly Mock<ILogger<RealTimeExecutionService>> _mockLogger;
    private readonly IConfiguration _configuration;
    private readonly RealTimeExecutionService _service;

    public RealTimeExecutionServiceTests()
    {
        _mockTickStreamService = new Mock<ITickStreamService>();
        _mockVwapMonitorService = new Mock<IVWAPMonitorService>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockRedis = new Mock<IDatabase>();
        _mockLogger = new Mock<ILogger<RealTimeExecutionService>>();

        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["RealTimeExecution:MonitoringIntervalMs"] = "1000",
                ["RealTimeExecution:SpreadSpikeThreshold"] = "2.0",
                ["RealTimeExecution:LiquidityThreshold"] = "5000",
                ["RealTimeExecution:MaxConcurrentExecutions"] = "10",
                ["RealTimeExecution:DefaultThrottleDuration"] = "00:05:00",
                ["RealTimeExecution:EnableAdaptiveExecution"] = "true",
                ["RealTimeExecution:EnableSpreadMonitoring"] = "true",
                ["RealTimeExecution:EnableVwapGuidance"] = "true"
            })
            .Build();

        _mockRedisService.Setup(x => x.GetDatabaseAsync(0)).ReturnsAsync(_mockRedis.Object);

        _service = new RealTimeExecutionService(
            _mockTickStreamService.Object,
            _mockVwapMonitorService.Object,
            _mockRedisService.Object,
            _mockMarketDataService.Object,
            _configuration,
            _mockLogger.Object);
    }

    [Fact]
    public async Task StartMonitoringAsync_ShouldStartSuccessfully()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT", "GOOGL" };

        // Act
        await _service.StartMonitoringAsync(symbols);

        // Assert
        _service.GetStatus().Should().Be(RealTimeExecutionStatus.Active);
        _service.GetMonitoredSymbols().Should().BeEquivalentTo(symbols);
    }

    [Fact]
    public async Task StopMonitoringAsync_ShouldStopSuccessfully()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        await _service.StartMonitoringAsync(symbols);

        // Act
        await _service.StopMonitoringAsync();

        // Assert
        _service.GetStatus().Should().Be(RealTimeExecutionStatus.Stopped);
        _service.GetMonitoredSymbols().Should().BeEmpty();
    }

    [Fact]
    public async Task EvaluateExecutionStrategyAsync_WithGoodConditions_ShouldRecommendMarketOrder()
    {
        // Arrange
        var symbol = "AAPL";
        var side = OrderSide.Buy;
        var quantity = 100m;
        
        SetupMockMarketData(symbol, goodConditions: true);
        SetupMockVwapData(symbol, 150m);

        // Act
        var result = await _service.EvaluateExecutionStrategyAsync(symbol, side.ToSmaOrderSide(), quantity);

        // Assert
        result.Should().NotBeNull();
        result.Symbol.Should().Be(symbol);
        result.RecommendedOrderType.Should().Be(SmaOrderType.Market);
        result.Timing.Should().Be(ExecutionTiming.Immediate);
        result.Urgency.Should().Be(ExecutionUrgency.High);
        result.ConfidenceScore.Should().BeGreaterThan(0.5m);
        result.Reasoning.Should().NotBeEmpty();
    }

    [Fact]
    public async Task EvaluateExecutionStrategyAsync_WithPoorConditions_ShouldRecommendLimitOrder()
    {
        // Arrange
        var symbol = "AAPL";
        var side = OrderSide.Buy;
        var quantity = 100m;
        
        SetupMockMarketData(symbol, goodConditions: false);
        SetupMockVwapData(symbol, 150m);

        // Act
        var result = await _service.EvaluateExecutionStrategyAsync(symbol, side.ToSmaOrderSide(), quantity);

        // Assert
        result.Should().NotBeNull();
        result.Symbol.Should().Be(symbol);
        result.RecommendedOrderType.Should().Be(SmaOrderType.Limit);
        result.LimitPrice.Should().BeGreaterThan(0);
        result.Timing.Should().BeOneOf(ExecutionTiming.Patient, ExecutionTiming.Opportunistic);
        result.ConfidenceScore.Should().BeLessThanOrEqualTo(0.6m);
    }

    [Fact]
    public async Task IsExecutionAllowedAsync_WithNormalConditions_ShouldReturnTrue()
    {
        // Arrange
        var symbol = "AAPL";
        SetupMockMarketData(symbol, goodConditions: true);

        // Act
        var result = await _service.IsExecutionAllowedAsync(symbol);

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task IsExecutionAllowedAsync_WithSpreadSpike_ShouldReturnFalse()
    {
        // Arrange
        var symbol = "AAPL";
        SetupMockMarketData(symbol, goodConditions: true, hasSpreadSpike: true);

        // Act
        var result = await _service.IsExecutionAllowedAsync(symbol);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task IsExecutionAllowedAsync_WithVeryLowLiquidity_ShouldReturnFalse()
    {
        // Arrange
        var symbol = "AAPL";
        SetupMockMarketData(symbol, goodConditions: true, hasLowLiquidity: true);

        // Act
        var result = await _service.IsExecutionAllowedAsync(symbol);

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task GetOptimalExecutionPriceAsync_ForBuyOrder_ShouldReturnReasonablePrice()
    {
        // Arrange
        var symbol = "AAPL";
        var side = OrderSide.Buy;
        var quantity = 100m;
        
        SetupMockMarketData(symbol, goodConditions: true);
        SetupMockVwapData(symbol, 150m);

        // Act
        var result = await _service.GetOptimalExecutionPriceAsync(symbol, side.ToSmaOrderSide(), quantity);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeGreaterThan(0);
        result.Should().BeLessThanOrEqualTo(155m); // Should be reasonable relative to VWAP
    }

    [Fact]
    public async Task GetOptimalExecutionPriceAsync_ForSellOrder_ShouldReturnReasonablePrice()
    {
        // Arrange
        var symbol = "AAPL";
        var side = OrderSide.Sell;
        var quantity = 100m;
        
        SetupMockMarketData(symbol, goodConditions: true);
        SetupMockVwapData(symbol, 150m);

        // Act
        var result = await _service.GetOptimalExecutionPriceAsync(symbol, side.ToSmaOrderSide(), quantity);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeGreaterThan(0);
        result.Should().BeGreaterThanOrEqualTo(145m); // Should be reasonable relative to VWAP
    }

    [Fact]
    public async Task GetMicrostructureAnalysisAsync_ShouldReturnAnalysis()
    {
        // Arrange
        var symbol = "AAPL";
        SetupMockMarketData(symbol, goodConditions: true);

        // Act
        var result = await _service.GetMicrostructureAnalysisAsync(symbol);

        // Assert
        result.Should().NotBeNull();
        result.Symbol.Should().Be(symbol);
        result.BidPrice.Should().BeGreaterThan(0);
        result.AskPrice.Should().BeGreaterThan(0);
        result.Spread.Should().BeGreaterThan(0);
        result.SpreadPercent.Should().BeGreaterThan(0);
        result.Quality.Should().BeOneOf(MicrostructureQuality.Excellent, MicrostructureQuality.Good, MicrostructureQuality.Fair, MicrostructureQuality.Poor, MicrostructureQuality.VeryPoor);
        result.LiquidityLevel.Should().BeOneOf(LiquidityLevel.VeryHigh, LiquidityLevel.High, LiquidityLevel.Normal, LiquidityLevel.Low, LiquidityLevel.VeryLow);
    }

    [Fact]
    public async Task GetSpreadAnalysisAsync_ShouldReturnAnalysis()
    {
        // Arrange
        var symbol = "AAPL";
        SetupMockMarketData(symbol, goodConditions: true);

        // Act
        var result = await _service.GetSpreadAnalysisAsync(symbol);

        // Assert
        result.Should().NotBeNull();
        result.Symbol.Should().Be(symbol);
        result.CurrentSpread.Should().BeGreaterThan(0);
        result.AverageSpread5Min.Should().BeGreaterThan(0);
        result.AverageSpread1Hour.Should().BeGreaterThan(0);
        result.Trend.Should().BeOneOf(SpreadTrend.Tightening, SpreadTrend.Stable, SpreadTrend.Widening);
        result.Stability.Should().BeOneOf(SpreadStability.VeryStable, SpreadStability.Stable, SpreadStability.Moderate, SpreadStability.Unstable, SpreadStability.VeryUnstable);
    }

    [Fact]
    public async Task GetVwapExecutionAnalysisAsync_ShouldReturnAnalysis()
    {
        // Arrange
        var symbol = "AAPL";
        SetupMockMarketData(symbol, goodConditions: true);
        SetupMockVwapData(symbol, 150m);

        // Act
        var result = await _service.GetVwapExecutionAnalysisAsync(symbol);

        // Assert
        result.Should().NotBeNull();
        result.Symbol.Should().Be(symbol);
        result.CurrentVwap.Should().Be(150m);
        result.CurrentPrice.Should().BeGreaterThan(0);
        result.Recommendation.Should().BeOneOf(VwapExecutionRecommendation.ExecuteAboveVwap, VwapExecutionRecommendation.ExecuteBelowVwap, VwapExecutionRecommendation.ExecuteAtVwap, VwapExecutionRecommendation.WaitForBetterPrice, VwapExecutionRecommendation.ExecuteImmediately);
        result.OptimalTiming.Should().BeOneOf(ExecutionTiming.Immediate, ExecutionTiming.Opportunistic, ExecutionTiming.Patient, ExecutionTiming.EndOfDay);
    }

    [Fact]
    public async Task GetLiquidityAssessmentAsync_ShouldReturnAssessment()
    {
        // Arrange
        var symbol = "AAPL";
        SetupMockMarketData(symbol, goodConditions: true);

        // Act
        var result = await _service.GetLiquidityAssessmentAsync(symbol);

        // Assert
        result.Should().NotBeNull();
        result.Symbol.Should().Be(symbol);
        result.BidLiquidity.Should().BeGreaterThan(0);
        result.AskLiquidity.Should().BeGreaterThan(0);
        result.TotalLiquidity.Should().BeGreaterThan(0);
        result.LiquidityScore.Should().BeInRange(0m, 1m);
        result.Level.Should().BeOneOf(LiquidityLevel.VeryHigh, LiquidityLevel.High, LiquidityLevel.Normal, LiquidityLevel.Low, LiquidityLevel.VeryLow);
        result.MaxRecommendedSize.Should().BeGreaterThan(0);
        result.Trend.Should().BeOneOf(LiquidityTrend.Improving, LiquidityTrend.Stable, LiquidityTrend.Deteriorating);
    }

    [Fact]
    public async Task ThrottleExecutionAsync_ShouldThrottleExecution()
    {
        // Arrange
        var symbol = "AAPL";
        var duration = TimeSpan.FromMinutes(5);
        var reason = "Test throttle";

        var throttleEventFired = false;
        _service.ExecutionThrottled += (_, _) => throttleEventFired = true;

        // Act
        await _service.ThrottleExecutionAsync(symbol, duration, reason);

        // Assert
        var throttleStatus = await _service.GetExecutionThrottleStatusAsync(symbol);
        throttleStatus.Should().NotBeNull();
        throttleStatus!.IsThrottled.Should().BeTrue();
        throttleStatus.Reason.Should().Be(reason);
        throttleEventFired.Should().BeTrue();

        var isAllowed = await _service.IsExecutionAllowedAsync(symbol);
        isAllowed.Should().BeFalse();
    }

    [Fact]
    public async Task RemoveExecutionThrottleAsync_ShouldRemoveThrottle()
    {
        // Arrange
        var symbol = "AAPL";
        await _service.ThrottleExecutionAsync(symbol, TimeSpan.FromMinutes(5), "Test");

        // Act
        await _service.RemoveExecutionThrottleAsync(symbol);

        // Assert
        var throttleStatus = await _service.GetExecutionThrottleStatusAsync(symbol);
        throttleStatus.Should().BeNull();
    }

    [Fact]
    public async Task GetExecutionStatisticsAsync_ShouldReturnStatistics()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        await _service.StartMonitoringAsync(symbols);

        // Act
        var result = await _service.GetExecutionStatisticsAsync();

        // Assert
        result.Should().NotBeNull();
        result.LastUpdated.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        result.TotalExecutions.Should().BeGreaterThanOrEqualTo(0);
        result.ThrottledExecutions.Should().BeGreaterThanOrEqualTo(0);
        result.AverageSlippage.Should().BeGreaterThanOrEqualTo(0);
        result.AverageExecutionTime.Should().BeGreaterThanOrEqualTo(0);
        result.MonitoringDuration.Should().BeGreaterThanOrEqualTo(TimeSpan.Zero);
    }

    [Theory]
    [InlineData(OrderSide.Buy)]
    [InlineData(OrderSide.Sell)]
    public async Task EvaluateExecutionStrategyAsync_WithDifferentSides_ShouldHandleCorrectly(OrderSide side)
    {
        // Arrange
        var symbol = "AAPL";
        var quantity = 100m;
        
        SetupMockMarketData(symbol, goodConditions: true);
        SetupMockVwapData(symbol, 150m);

        // Act
        var result = await _service.EvaluateExecutionStrategyAsync(symbol, side.ToSmaOrderSide(), quantity);

        // Assert
        result.Should().NotBeNull();
        result.Symbol.Should().Be(symbol);
        result.ConfidenceScore.Should().BeGreaterThan(0);
        
        if (result.RecommendedOrderType == SmaOrderType.Limit)
        {
            result.LimitPrice.Should().BeGreaterThan(0);
        }
    }

    private void SetupMockMarketData(string symbol, bool goodConditions, bool hasSpreadSpike = false, bool hasLowLiquidity = false)
    {
        var bars = CreateMockBars(symbol, goodConditions);
        var mockPage = TestDataFactory.CreateMockBarPage(bars);
        _mockMarketDataService.Setup(x => x.GetStockBarsAsync(symbol, It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(mockPage);
    }

    private void SetupMockVwapData(string symbol, decimal vwap)
    {
        var vwapData = new VWAPData(
            Symbol: symbol,
            VWAP: vwap,
            CurrentPrice: vwap,
            DeviationPercent: 0m,
            CumulativeVolume: 1000000,
            CumulativeValue: vwap * 1000000,
            Timestamp: DateTime.UtcNow,
            TradeCount: 100,
            Trend: VWAPTrend.AtVWAP
        );

        _mockVwapMonitorService.Setup(x => x.GetCurrentVWAPAsync(symbol))
            .ReturnsAsync(vwapData);
    }

    private List<IBar> CreateMockBars(string symbol, bool goodConditions)
    {
        var bars = new List<IBar>();
        var basePrice = 150m;
        
        for (int i = 0; i < 10; i++)
        {
            var mockBar = new Mock<IBar>();
            var price = basePrice + (i * 0.1m);
            
            mockBar.Setup(x => x.Close).Returns(price);
            mockBar.Setup(x => x.Open).Returns(price - 0.5m);
            mockBar.Setup(x => x.High).Returns(price + (goodConditions ? 1m : 5m)); // Wider range for poor conditions
            mockBar.Setup(x => x.Low).Returns(price - (goodConditions ? 1m : 5m));
            mockBar.Setup(x => x.Volume).Returns(goodConditions ? 1000000 : 10000); // Lower volume for poor conditions
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddMinutes(-i));

            bars.Add(mockBar.Object);
        }

        return bars.OrderBy(b => b.TimeUtc).ToList();
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
