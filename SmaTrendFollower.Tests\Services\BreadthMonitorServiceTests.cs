using Alpaca.Markets;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

[TestTimeout(TestTimeouts.Unit)]
[Trait("Category", TestCategories.Unit)]
public class BreadthMonitorServiceTests : IDisposable
{
    private readonly Mock<IDynamicUniverseProvider> _mockUniverseProvider;
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<ITickStreamService> _mockTickStreamService;
    private readonly Mock<IDatabase> _mockRedis;
    private readonly Mock<ILogger<BreadthMonitorService>> _mockLogger;
    private readonly IConfiguration _configuration;
    private readonly BreadthMonitorService _service;

    public BreadthMonitorServiceTests()
    {
        _mockUniverseProvider = new Mock<IDynamicUniverseProvider>();
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockTickStreamService = new Mock<ITickStreamService>();
        _mockRedis = new Mock<IDatabase>();
        _mockLogger = new Mock<ILogger<BreadthMonitorService>>();

        _configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["BreadthMonitor:UpdateIntervalSeconds"] = "30",
                ["BreadthMonitor:MinUniverseSize"] = "100",
                ["BreadthMonitor:ExtremeBreadthThreshold"] = "0.9",
                ["BreadthMonitor:DivergenceThreshold"] = "0.3",
                ["BreadthMonitor:MomentumLookbackDays"] = "20",
                ["BreadthMonitor:EnableRealTimeUpdates"] = "true",
                ["BreadthMonitor:CacheExpiryMinutes"] = "5"
            })
            .Build();

        _mockRedisService.Setup(x => x.GetDatabaseAsync(-1)).ReturnsAsync(_mockRedis.Object);

        _service = new BreadthMonitorService(
            _mockUniverseProvider.Object,
            _mockMarketDataService.Object,
            _mockRedisService.Object,
            _mockTickStreamService.Object,
            _configuration,
            _mockLogger.Object);
    }

    [Fact]
    public async Task StartMonitoringAsync_ShouldStartSuccessfully()
    {
        // Arrange
        SetupMockUniverse();

        // Act
        await _service.StartMonitoringAsync();

        // Assert
        _service.GetStatus().Should().Be(BreadthMonitorStatus.Active);
    }

    [Fact]
    public async Task StopMonitoringAsync_ShouldStopSuccessfully()
    {
        // Arrange
        SetupMockUniverse();
        await _service.StartMonitoringAsync();

        // Act
        await _service.StopMonitoringAsync();

        // Assert
        _service.GetStatus().Should().Be(BreadthMonitorStatus.Stopped);
    }

    [Fact]
    public async Task RefreshBreadthAnalysisAsync_ShouldReturnComprehensiveAnalysis()
    {
        // Arrange
        SetupMockUniverse();
        SetupMockMarketData();

        // Act
        var result = await _service.RefreshBreadthAnalysisAsync();

        // Assert
        result.Should().NotBeNull();
        result.UniverseSize.Should().BeGreaterThan(0);
        result.AdvanceDecline.Should().NotBeNull();
        result.MovingAverageBreadth.Should().NotBeNull();
        result.NewHighsLows.Should().NotBeNull();
        result.Momentum.Should().NotBeNull();
        result.Divergence.Should().NotBeNull();
        result.Regime.Should().BeOneOf(BreadthRegime.Healthy, BreadthRegime.Deteriorating, BreadthRegime.Weak, BreadthRegime.Extreme, BreadthRegime.Recovering);
        result.SignalStrength.Should().BeOneOf(BreadthSignalStrength.VeryWeak, BreadthSignalStrength.Weak, BreadthSignalStrength.Neutral, BreadthSignalStrength.Strong, BreadthSignalStrength.VeryStrong);
        result.OverallBreadthScore.Should().BeInRange(0m, 1m);
        result.Summary.Should().NotBeEmpty();
    }

    [Fact]
    public async Task GetAdvanceDeclineStatsAsync_ShouldCalculateCorrectly()
    {
        // Arrange
        SetupMockUniverse();
        SetupMockMarketData();

        // Act
        var result = await _service.GetAdvanceDeclineStatsAsync();

        // Assert
        result.Should().NotBeNull();
        result.Advancing.Should().BeGreaterThanOrEqualTo(0);
        result.Declining.Should().BeGreaterThanOrEqualTo(0);
        result.Unchanged.Should().BeGreaterThanOrEqualTo(0);
        result.AdvanceDeclineRatio.Should().BeGreaterThanOrEqualTo(0);
        result.AdvanceDeclinePercent.Should().BeInRange(0m, 100m);
        result.Trend.Should().BeOneOf(AdvanceDeclineTrend.Improving, AdvanceDeclineTrend.Stable, AdvanceDeclineTrend.Deteriorating);
    }

    [Fact]
    public async Task GetMovingAverageBreadthAsync_ShouldCalculateCorrectly()
    {
        // Arrange
        SetupMockUniverse();
        SetupMockMarketData();

        // Act
        var result = await _service.GetMovingAverageBreadthAsync();

        // Assert
        result.Should().NotBeNull();
        result.Above50SMA.Should().BeGreaterThanOrEqualTo(0);
        result.Below50SMA.Should().BeGreaterThanOrEqualTo(0);
        result.PercentAbove50SMA.Should().BeInRange(0m, 100m);
        result.Above200SMA.Should().BeGreaterThanOrEqualTo(0);
        result.Below200SMA.Should().BeGreaterThanOrEqualTo(0);
        result.PercentAbove200SMA.Should().BeInRange(0m, 100m);
        result.Trend50SMA.Should().BeOneOf(MovingAverageTrend.Improving, MovingAverageTrend.Stable, MovingAverageTrend.Deteriorating);
        result.Trend200SMA.Should().BeOneOf(MovingAverageTrend.Improving, MovingAverageTrend.Stable, MovingAverageTrend.Deteriorating);
    }

    [Fact]
    public async Task GetNewHighsLowsStatsAsync_ShouldCalculateCorrectly()
    {
        // Arrange
        SetupMockUniverse();
        SetupMockMarketData();

        // Act
        var result = await _service.GetNewHighsLowsStatsAsync();

        // Assert
        result.Should().NotBeNull();
        result.NewHighs52Week.Should().BeGreaterThanOrEqualTo(0);
        result.NewLows52Week.Should().BeGreaterThanOrEqualTo(0);
        result.NewHighs20Day.Should().BeGreaterThanOrEqualTo(0);
        result.NewLows20Day.Should().BeGreaterThanOrEqualTo(0);
        result.HighLowRatio.Should().BeGreaterThanOrEqualTo(0);
        result.Trend.Should().BeOneOf(NewHighsLowsTrend.Improving, NewHighsLowsTrend.Stable, NewHighsLowsTrend.Deteriorating);
    }

    [Fact]
    public async Task SupportsBullishSignalsAsync_WithHealthyBreadth_ShouldReturnTrue()
    {
        // Arrange
        SetupMockUniverse();
        SetupMockMarketData();
        SetupMockCachedBreadth(BreadthRegime.Healthy, BreadthSignalStrength.Strong, 0.8m);

        // Act
        var result = await _service.SupportsBullishSignalsAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task SupportsBullishSignalsAsync_WithWeakBreadth_ShouldReturnFalse()
    {
        // Arrange
        SetupMockUniverse();
        SetupMockMarketData();
        SetupMockCachedBreadth(BreadthRegime.Weak, BreadthSignalStrength.VeryWeak, 0.1m);

        // Act
        var result = await _service.SupportsBullishSignalsAsync();

        // Assert
        result.Should().BeFalse();
    }

    [Fact]
    public async Task SuggestsDefensivePositioningAsync_WithWeakBreadth_ShouldReturnTrue()
    {
        // Arrange
        SetupMockUniverse();
        SetupMockMarketData();
        SetupMockCachedBreadth(BreadthRegime.Weak, BreadthSignalStrength.VeryWeak, 0.1m);

        // Act
        var result = await _service.SuggestsDefensivePositioningAsync();

        // Assert
        result.Should().BeTrue();
    }

    [Fact]
    public async Task GetBreadthAdjustedPositionSizeAsync_ShouldAdjustBasedOnStrength()
    {
        // Arrange
        var baseSize = 100m;
        SetupMockUniverse();
        SetupMockMarketData();
        SetupMockCachedBreadth(BreadthRegime.Healthy, BreadthSignalStrength.VeryStrong, 0.9m);

        // Act
        var result = await _service.GetBreadthAdjustedPositionSizeAsync(baseSize);

        // Assert
        result.Should().BeGreaterThan(baseSize); // Should be increased for very strong breadth
    }

    [Theory]
    [InlineData(BreadthSignalStrength.VeryStrong, 120)]
    [InlineData(BreadthSignalStrength.Strong, 110)]
    [InlineData(BreadthSignalStrength.Neutral, 100)]
    [InlineData(BreadthSignalStrength.Weak, 80)]
    [InlineData(BreadthSignalStrength.VeryWeak, 60)]
    public async Task GetBreadthAdjustedPositionSizeAsync_WithDifferentStrengths_ShouldAdjustCorrectly(
        BreadthSignalStrength strength, decimal expectedSize)
    {
        // Arrange
        var baseSize = 100m;
        SetupMockUniverse();
        SetupMockMarketData();
        SetupMockCachedBreadth(BreadthRegime.Healthy, strength, 0.5m);

        // Act
        var result = await _service.GetBreadthAdjustedPositionSizeAsync(baseSize);

        // Assert
        result.Should().Be(expectedSize);
    }

    [Fact]
    public async Task GetMonitoringStatsAsync_ShouldReturnStatistics()
    {
        // Arrange
        SetupMockUniverse();
        await _service.StartMonitoringAsync();

        // Act
        var result = await _service.GetMonitoringStatsAsync();

        // Assert
        result.Should().NotBeNull();
        result.MonitoringStartedAt.Should().BeCloseTo(DateTime.UtcNow, TimeSpan.FromMinutes(1));
        result.SuccessRate.Should().BeInRange(0m, 1m);
        result.TotalUpdates.Should().BeGreaterThanOrEqualTo(0);
        result.FailedUpdates.Should().BeGreaterThanOrEqualTo(0);
    }

    [Fact]
    public void GetMonitoredUniverseSize_AfterAnalysis_ShouldReturnSize()
    {
        // Arrange
        SetupMockUniverse();
        SetupMockMarketData();

        // Act & Assert (will be set after first analysis)
        var initialSize = _service.GetMonitoredUniverseSize();
        initialSize.Should().Be(0); // No analysis performed yet
    }

    [Fact]
    public async Task GetCachedBreadthAsync_WithValidCache_ShouldReturnCachedData()
    {
        // Arrange
        var cachedAnalysis = new RealTimeBreadthAnalysis
        {
            AnalyzedAt = DateTime.UtcNow.AddMinutes(-2),
            UniverseSize = 500,
            Regime = BreadthRegime.Healthy,
            SignalStrength = BreadthSignalStrength.Strong,
            OverallBreadthScore = 0.75m
        };

        _mockRedis.Setup(x => x.StringGetAsync(It.Is<RedisKey>(k => k == "breadth:analysis:current"), It.IsAny<CommandFlags>()))
            .ReturnsAsync(System.Text.Json.JsonSerializer.Serialize(cachedAnalysis));

        // Act
        var result = await _service.GetCachedBreadthAsync();

        // Assert
        result.Should().NotBeNull();
        result!.Regime.Should().Be(BreadthRegime.Healthy);
        result.SignalStrength.Should().Be(BreadthSignalStrength.Strong);
        result.OverallBreadthScore.Should().Be(0.75m);
    }

    private void SetupMockUniverse()
    {
        var symbols = new List<string> { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA" };
        _mockUniverseProvider.Setup(x => x.GetCachedUniverseAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(symbols);
    }

    private void SetupMockMarketData()
    {
        var symbols = new List<string> { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA" };
        
        foreach (var symbol in symbols)
        {
            var bars = CreateMockBarsForSymbol(symbol);
            var mockPage = TestDataFactory.CreateMockBarPage(bars);
            _mockMarketDataService.Setup(x => x.GetStockBarsAsync(It.Is<string>(s => s == symbol), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
                .ReturnsAsync(mockPage);
        }
    }

    private void SetupMockCachedBreadth(BreadthRegime regime, BreadthSignalStrength strength, decimal score)
    {
        var cachedAnalysis = new RealTimeBreadthAnalysis
        {
            AnalyzedAt = DateTime.UtcNow.AddMinutes(-1),
            UniverseSize = 5,
            Regime = regime,
            SignalStrength = strength,
            OverallBreadthScore = score,
            AdvanceDecline = new AdvanceDeclineStats(),
            MovingAverageBreadth = new MovingAverageBreadthStats(),
            NewHighsLows = new NewHighsLowsStats(),
            Momentum = new BreadthMomentumAnalysis(),
            Divergence = new BreadthDivergenceAnalysis()
        };

        _mockRedis.Setup(x => x.StringGetAsync(It.Is<RedisKey>(k => k == "breadth:analysis:current"), It.IsAny<CommandFlags>()))
            .ReturnsAsync(System.Text.Json.JsonSerializer.Serialize(cachedAnalysis));
    }

    private List<IBar> CreateMockBarsForSymbol(string symbol)
    {
        var bars = new List<IBar>();
        var basePrice = symbol switch
        {
            "AAPL" => 150m,
            "MSFT" => 300m,
            "GOOGL" => 2500m,
            "AMZN" => 3000m,
            "TSLA" => 800m,
            _ => 100m
        };

        // Create 300 days of data for SMA calculations
        for (int i = 0; i < 300; i++)
        {
            var price = basePrice + (i * 0.1m); // Slight upward trend
            var mockBar = new Mock<IBar>();
            
            mockBar.Setup(x => x.Close).Returns(price);
            mockBar.Setup(x => x.Open).Returns(price - 1);
            mockBar.Setup(x => x.High).Returns(price + 2);
            mockBar.Setup(x => x.Low).Returns(price - 2);
            mockBar.Setup(x => x.Volume).Returns(1000000);
            mockBar.Setup(x => x.TimeUtc).Returns(DateTime.UtcNow.AddDays(-i));

            bars.Add(mockBar.Object);
        }

        return bars.OrderBy(b => b.TimeUtc).ToList();
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
