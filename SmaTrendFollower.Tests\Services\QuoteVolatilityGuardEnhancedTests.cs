using FluentAssertions;
using Microsoft.Extensions.Logging;
using NSubstitute;
using SmaTrendFollower.Services;
using SmaTrendFollower.Tests.TestHelpers;
using StackExchange.Redis;
using Xunit;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Enhanced tests for QuoteVolatilityGuard focusing on halt key setting and z-score threshold behavior
/// </summary>
public class QuoteVolatilityGuardEnhancedTests : IDisposable
{
    private readonly RedisTestFixture _redisFixture;
    private readonly IDatabase _redis;
    private readonly IConnectionMultiplexer _connectionMultiplexer;
    private readonly ILogger<QuoteVolatilityGuard> _logger;
    private readonly QuoteVolatilityGuard _guard;

    public QuoteVolatilityGuardEnhancedTests()
    {
        _redisFixture = new RedisTestFixture();
        _redis = _redisFixture.Database;
        _connectionMultiplexer = _redisFixture.Connection;
        _logger = Substitute.For<ILogger<QuoteVolatilityGuard>>();
        _guard = new QuoteVolatilityGuard(_connectionMultiplexer, _logger);
    }

    [Fact]
    public async Task GuardFlagsZScoreAbove2_SetsHaltKey()
    {
        // Arrange
        const string symbol = "TEST";
        const decimal normalBid = 100.00m;
        const decimal normalAsk = 100.10m;
        const decimal spikeBid = 100.00m;
        const decimal spikeAsk = 100.50m; // Large spread spike

        // Act - Feed normal quotes to build baseline
        for (int i = 0; i < 120; i++)
        {
            _guard.OnQuote(symbol, normalBid, normalAsk);
        }

        // Feed spike quote that should trigger halt
        _guard.OnQuote(symbol, spikeBid, spikeAsk);

        // Wait a moment for async halt processing
        await Task.Delay(100);

        // Assert
        var haltKeyExists = await _redis.KeyExistsAsync($"halt:{symbol}");
        haltKeyExists.Should().BeTrue("Halt key should be set when z-score exceeds threshold");

        var isHalted = await _guard.IsTradingHaltedAsync(symbol);
        isHalted.Should().BeTrue("Trading should be halted for symbol with high volatility");
    }

    [Fact]
    public async Task GuardWithNormalVolatility_DoesNotSetHaltKey()
    {
        // Arrange
        const string symbol = "NORMAL";
        const decimal bid = 50.00m;
        const decimal ask = 50.05m; // Normal spread

        // Act - Feed normal quotes
        for (int i = 0; i < 150; i++)
        {
            var bidVariation = bid + (decimal)(i % 10) * 0.01m;
            var askVariation = ask + (decimal)(i % 10) * 0.01m;
            _guard.OnQuote(symbol, bidVariation, askVariation);
        }

        await Task.Delay(50);

        // Assert
        var haltKeyExists = await _redis.KeyExistsAsync($"halt:{symbol}");
        haltKeyExists.Should().BeFalse("Halt key should not be set for normal volatility");

        var isHalted = await _guard.IsTradingHaltedAsync(symbol);
        isHalted.Should().BeFalse("Trading should not be halted for normal volatility");
    }

    [Fact]
    public void OnQuote_CalculatesZScoreCorrectly()
    {
        // Arrange
        const string symbol = "ZSCORE";
        const decimal baseBid = 200.00m;
        const decimal baseAsk = 200.10m;

        // Act - Build baseline with consistent spread
        for (int i = 0; i < 120; i++)
        {
            _guard.OnQuote(symbol, baseBid, baseAsk);
        }

        // Get stats before spike
        var statsBefore = _guard.GetSpreadStats(symbol);
        statsBefore.Should().NotBeNull();
        statsBefore.Value.Count.Should().Be(120);

        // Add a quote with significantly higher spread
        const decimal spikeBid = 200.00m;
        const decimal spikeAsk = 200.40m; // 4x normal spread
        _guard.OnQuote(symbol, spikeBid, spikeAsk);

        // Assert
        var statsAfter = _guard.GetSpreadStats(symbol);
        statsAfter.Should().NotBeNull();
        statsAfter.Value.Count.Should().Be(121);
        
        // The spike should increase the standard deviation
        statsAfter.Value.StandardDeviation.Should().BeGreaterThan(statsBefore.Value.StandardDeviation);
    }

    [Fact]
    public async Task HaltKey_HasCorrectTTL()
    {
        // Arrange
        const string symbol = "TTL_TEST";
        const decimal normalBid = 75.00m;
        const decimal normalAsk = 75.05m;
        const decimal spikeBid = 75.00m;
        const decimal spikeAsk = 75.25m; // Large spread

        // Act - Build baseline and trigger halt
        for (int i = 0; i < 120; i++)
        {
            _guard.OnQuote(symbol, normalBid, normalAsk);
        }
        
        _guard.OnQuote(symbol, spikeBid, spikeAsk);
        await Task.Delay(100);

        // Assert
        var haltKey = $"halt:{symbol}";
        var keyExists = await _redis.KeyExistsAsync(haltKey);
        keyExists.Should().BeTrue();

        var ttl = await _redis.KeyTimeToLiveAsync(haltKey);
        ttl.Should().NotBeNull();
        ttl.Value.TotalMinutes.Should().BeApproximately(2.0, 0.5); // 2-minute TTL with some tolerance
    }

    [Fact]
    public async Task MultipleSymbols_IndependentHaltStates()
    {
        // Arrange
        const string symbol1 = "AAPL";
        const string symbol2 = "MSFT";
        const decimal normalBid = 150.00m;
        const decimal normalAsk = 150.10m;
        const decimal spikeBid = 150.00m;
        const decimal spikeAsk = 150.50m;

        // Act - Build baseline for both symbols
        for (int i = 0; i < 120; i++)
        {
            _guard.OnQuote(symbol1, normalBid, normalAsk);
            _guard.OnQuote(symbol2, normalBid, normalAsk);
        }

        // Trigger halt only for symbol1
        _guard.OnQuote(symbol1, spikeBid, spikeAsk);
        await Task.Delay(100);

        // Assert
        var symbol1Halted = await _guard.IsTradingHaltedAsync(symbol1);
        var symbol2Halted = await _guard.IsTradingHaltedAsync(symbol2);

        symbol1Halted.Should().BeTrue("Symbol1 should be halted due to volatility spike");
        symbol2Halted.Should().BeFalse("Symbol2 should not be halted");

        // Verify independent stats
        var stats1 = _guard.GetSpreadStats(symbol1);
        var stats2 = _guard.GetSpreadStats(symbol2);

        stats1.Should().NotBeNull();
        stats2.Should().NotBeNull();
        stats1.Value.Count.Should().Be(121); // 120 + 1 spike
        stats2.Value.Count.Should().Be(120); // Only normal quotes
    }

    [Fact]
    public async Task ClearSpreadStats_RemovesStatsAndHaltState()
    {
        // Arrange
        const string symbol = "CLEAR_TEST";
        const decimal bid = 100.00m;
        const decimal ask = 100.50m; // Large spread to trigger halt

        // Build stats and trigger halt
        for (int i = 0; i < 120; i++)
        {
            _guard.OnQuote(symbol, bid, ask);
        }
        await Task.Delay(100);

        // Verify halt is active
        var isHaltedBefore = await _guard.IsTradingHaltedAsync(symbol);
        isHaltedBefore.Should().BeTrue();

        // Act
        _guard.ClearSpreadStats(symbol);

        // Assert
        var statsAfterClear = _guard.GetSpreadStats(symbol);
        statsAfterClear.Should().BeNull("Stats should be cleared");

        // Note: Halt key in Redis may still exist due to TTL, but stats are cleared
        // This tests the internal state management
    }

    [Fact]
    public void GetSpreadStats_WithInsufficientData_ReturnsNull()
    {
        // Arrange
        const string symbol = "INSUFFICIENT";
        const decimal bid = 50.00m;
        const decimal ask = 50.05m;

        // Act - Add only a few quotes (less than required for stats)
        for (int i = 0; i < 5; i++)
        {
            _guard.OnQuote(symbol, bid, ask);
        }

        // Assert
        var stats = _guard.GetSpreadStats(symbol);
        stats.Should().BeNull("Stats should not be available with insufficient data");
    }

    [Theory]
    [InlineData(100.00, 100.05, 0.0005)] // 0.05% spread
    [InlineData(50.00, 50.10, 0.002)]    // 0.2% spread  
    [InlineData(200.00, 200.50, 0.0025)] // 0.25% spread
    public void OnQuote_CalculatesSpreadPercentageCorrectly(decimal bid, decimal ask, double expectedSpreadPct)
    {
        // Arrange
        const string symbol = "SPREAD_TEST";

        // Act
        _guard.OnQuote(symbol, bid, ask);

        // Assert
        var stats = _guard.GetSpreadStats(symbol);
        if (stats.HasValue && stats.Value.Count > 0)
        {
            // The exact spread calculation is internal, but we can verify the quote was processed
            stats.Value.Count.Should().Be(1);
        }
        
        // Verify the spread percentage calculation logic
        var midPrice = (bid + ask) / 2;
        var spread = ask - bid;
        var actualSpreadPct = (double)(spread / midPrice);
        
        actualSpreadPct.Should().BeApproximately(expectedSpreadPct, 0.0001);
    }

    [Fact]
    public async Task HaltKey_CoexistsWithAnomalyDetectorHalts()
    {
        // Arrange
        const string symbol = "COEXIST";
        
        // Manually set an anomaly detector halt key
        await _redis.StringSetAsync($"halt:{symbol}", "anomaly_detected", TimeSpan.FromMinutes(5));
        
        // Verify anomaly halt is active
        var isHaltedByAnomaly = await _guard.IsTradingHaltedAsync(symbol);
        isHaltedByAnomaly.Should().BeTrue("Should detect existing anomaly halt");

        // Act - Trigger quote volatility halt
        for (int i = 0; i < 120; i++)
        {
            _guard.OnQuote(symbol, 100.00m, 100.05m);
        }
        _guard.OnQuote(symbol, 100.00m, 100.50m); // Spike
        await Task.Delay(100);

        // Assert
        var isStillHalted = await _guard.IsTradingHaltedAsync(symbol);
        isStillHalted.Should().BeTrue("Should remain halted due to either/both halt reasons");
        
        // The halt key should still exist (either from anomaly or quote volatility)
        var haltKeyExists = await _redis.KeyExistsAsync($"halt:{symbol}");
        haltKeyExists.Should().BeTrue();
    }

    public void Dispose()
    {
        _guard?.Dispose();
        _redisFixture?.Dispose();
    }
}
