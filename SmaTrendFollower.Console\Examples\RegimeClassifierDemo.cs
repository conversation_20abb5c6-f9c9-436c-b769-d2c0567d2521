using Microsoft.Extensions.Logging;
using SmaTrendFollower.MachineLearning.ModelTraining;
using SmaTrendFollower.Models;

namespace SmaTrendFollower.Examples;

/// <summary>
/// Demo program to test the regime classification training and prediction functionality
/// </summary>
public static class RegimeClassifierDemo
{
    private static readonly ILogger Logger = LoggerFactory.Create(builder => 
        builder.AddConsole().SetMinimumLevel(LogLevel.Information))
        .CreateLogger(typeof(RegimeClassifierDemo));

    /// <summary>
    /// Main entry point for the regime classifier demo
    /// </summary>
    public static async Task Main(string[] args)
    {
        try
        {
            Logger.LogInformation("🚀 Starting Regime Classifier Demo...");
            Logger.LogInformation("");

            // Test 1: Train the model
            await TestModelTrainingAsync();

            // Test 2: Test regime labeling logic
            TestRegimeLabelingLogic();

            Logger.LogInformation("");
            Logger.LogInformation("✅ Regime Classifier Demo completed successfully!");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "❌ Error during regime classifier demo");
        }
    }

    /// <summary>
    /// Tests the model training functionality
    /// </summary>
    private static async Task TestModelTrainingAsync()
    {
        Logger.LogInformation("📊 Testing Model Training...");
        
        try
        {
            var csvPath = "Model/regime.csv";
            var modelPath = "Model/regime_model_demo.zip";

            if (!File.Exists(csvPath))
            {
                Logger.LogWarning("Training data file not found: {CsvPath}", csvPath);
                Logger.LogInformation("Skipping model training test");
                return;
            }

            var result = await TrainRegimeClassifier.TrainModelAsync(csvPath, modelPath);

            if (result.Success)
            {
                Logger.LogInformation("✅ Model training successful!");
                Logger.LogInformation("   📈 Accuracy: {Accuracy:P2}", result.Accuracy);
                Logger.LogInformation("   📊 Total Samples: {TotalSamples}", result.TotalSamples);
                Logger.LogInformation("   🎯 Training Samples: {TrainingSamples}", result.TrainingSamples);
                Logger.LogInformation("   💾 Model saved to: {ModelPath}", result.ModelPath);
            }
            else
            {
                Logger.LogError("❌ Model training failed: {Error}", result.ErrorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during model training test");
        }
    }

    /// <summary>
    /// Tests the regime labeling logic used for training data generation
    /// </summary>
    private static void TestRegimeLabelingLogic()
    {
        Logger.LogInformation("🏷️ Testing Regime Labeling Logic...");

        var testCases = new[]
        {
            new { SPX_Ret = -0.04f, VIX_Level = 25.0, VIX_Change = -0.1f, Expected = MarketRegime.Panic, Description = "Panic: SPX_Ret < -0.03" },
            new { SPX_Ret = 0.01f, VIX_Level = 35.0, VIX_Change = 0.05f, Expected = MarketRegime.Panic, Description = "Panic: VIX_Level >= 30" },
            new { SPX_Ret = -0.02f, VIX_Level = 20.0, VIX_Change = 0.05f, Expected = MarketRegime.TrendingDown, Description = "TrendingDown: SPX_Ret < -0.012 AND VIX_Change > 0" },
            new { SPX_Ret = 0.02f, VIX_Level = 20.0, VIX_Change = -0.05f, Expected = MarketRegime.TrendingUp, Description = "TrendingUp: SPX_Ret > 0.012 AND VIX_Change < 0" },
            new { SPX_Ret = 0.005f, VIX_Level = 20.0, VIX_Change = 0.01f, Expected = MarketRegime.Sideways, Description = "Sideways: Default case" }
        };

        foreach (var testCase in testCases)
        {
            var actual = CalculateRegimeLabel(testCase.SPX_Ret, testCase.VIX_Level, testCase.VIX_Change);
            var status = actual == testCase.Expected ? "✅" : "❌";
            
            Logger.LogInformation("   {Status} {Description}", status, testCase.Description);
            Logger.LogInformation("      Input: SPX={SPX:P2}, VIX={VIX:F1}, VIXΔ={VIXChange:P2}", 
                testCase.SPX_Ret, testCase.VIX_Level, testCase.VIX_Change);
            Logger.LogInformation("      Expected: {Expected}, Actual: {Actual}", testCase.Expected, actual);
            
            if (actual != testCase.Expected)
            {
                Logger.LogError("      ❌ Mismatch detected!");
            }
        }
    }

    /// <summary>
    /// Calculates regime label using the same logic as the training data generation
    /// </summary>
    private static MarketRegime CalculateRegimeLabel(float spxRet, double vixLevel, float vixChange)
    {
        // Historical label rules:
        // • Panic: VIX_Level ≥ 30 OR SPX_Ret < −0.03
        // • TrendingDown: SPX_Ret < −0.012 AND VIX_Change > 0
        // • TrendingUp: SPX_Ret > +0.012 AND VIX_Change < 0
        // • Else: Sideways
        
        if (vixLevel >= 30 || spxRet < -0.03f)
            return MarketRegime.Panic;
        
        if (spxRet < -0.012f && vixChange > 0)
            return MarketRegime.TrendingDown;
        
        if (spxRet > 0.012f && vixChange < 0)
            return MarketRegime.TrendingUp;
        
        return MarketRegime.Sideways;
    }
}
