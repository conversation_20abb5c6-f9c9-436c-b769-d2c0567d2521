using Microsoft.Extensions.Logging;
using Microsoft.ML;
using Moq;
using SmaTrendFollower.Models;
using SmaTrendFollower.Services;
using Xunit;
using FluentAssertions;

namespace SmaTrendFollower.Tests.Services;

/// <summary>
/// Unit tests for RegimeClassifierService
/// </summary>
public class RegimeClassifierServiceTests : IDisposable
{
    private readonly Mock<IMarketDataService> _mockMarketDataService;
    private readonly Mock<IOptimizedRedisConnectionService> _mockRedisService;
    private readonly Mock<IBreadthService> _mockBreadthService;
    private readonly Mock<ILogger<RegimeClassifierService>> _mockLogger;
    private readonly RegimeClassifierService _service;

    public RegimeClassifierServiceTests()
    {
        _mockMarketDataService = new Mock<IMarketDataService>();
        _mockRedisService = new Mock<IOptimizedRedisConnectionService>();
        _mockBreadthService = new Mock<IBreadthService>();
        _mockLogger = new Mock<ILogger<RegimeClassifierService>>();

        _service = new RegimeClassifierService(
            _mockMarketDataService.Object,
            _mockRedisService.Object,
            _mockBreadthService.Object,
            _mockLogger.Object);
    }

    [Fact]
    public async Task DetectTodayAsync_WithInsufficientData_ReturnsSidewaysDefault()
    {
        // Arrange
        var emptySpxBars = new List<IndexBar>();
        var emptyVixBars = new List<IndexBar>();

        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "I:SPX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(emptySpxBars);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "I:VIX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(emptyVixBars);

        var breadthAnalysis = new MarketBreadthAnalysis(
            DateTime.UtcNow,
            MarketRiskSentiment.Neutral,
            new AdvanceDeclineMetrics(100, 100, 0, 1.0m, 50m),
            new MovingAverageBreadth(50m, 50m, 50m, 100, 100, 100, 100),
            new NewHighsLowsMetrics(10, 10, 10, 10, 1.0m, 0m),
            50m,
            "Test analysis"
        );

        _mockBreadthService.Setup(x => x.GetCachedBreadthAsync())
            .ReturnsAsync(breadthAnalysis);

        // Act
        var result = await _service.DetectTodayAsync();

        // Assert
        result.Should().Be(MarketRegime.Sideways);
    }

    [Fact]
    public async Task DetectTodayAsync_WithValidData_ReturnsClassification()
    {
        // Arrange
        var spxBars = new List<IndexBar>
        {
            new IndexBar(DateTime.UtcNow.AddDays(-1), 4000m, 4100m, 3950m, 4050m, 1000000),
            new IndexBar(DateTime.UtcNow, 4050m, 4150m, 4000m, 4100m, 1200000)
        };

        var vixBars = new List<IndexBar>
        {
            new IndexBar(DateTime.UtcNow.AddDays(-1), 20m, 22m, 19m, 21m, 500000),
            new IndexBar(DateTime.UtcNow, 21m, 23m, 20m, 19m, 600000)
        };

        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "I:SPX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(spxBars);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "I:VIX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(vixBars);

        var breadthAnalysis = new MarketBreadthAnalysis(
            DateTime.UtcNow,
            MarketRiskSentiment.RiskOn,
            new AdvanceDeclineMetrics(150, 50, 0, 3.0m, 75m),
            new MovingAverageBreadth(75m, 70m, 65m, 150, 140, 130, 200),
            new NewHighsLowsMetrics(20, 5, 15, 10, 4.0m, 15m),
            75m,
            "Strong bullish breadth"
        );

        _mockBreadthService.Setup(x => x.GetCachedBreadthAsync())
            .ReturnsAsync(breadthAnalysis);

        // Act
        var result = await _service.DetectTodayAsync();

        // Assert
        result.Should().BeOneOf(MarketRegime.Sideways, MarketRegime.TrendingUp, MarketRegime.TrendingDown, MarketRegime.Panic);
    }

    [Fact]
    public async Task GetCachedRegimeAsync_WithNoCache_ReturnsNull()
    {
        // Arrange
        var mockDatabase = new Mock<StackExchange.Redis.IDatabase>();

        // Use a more specific setup to avoid optional parameter issues
        mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<StackExchange.Redis.RedisKey>(), It.IsAny<StackExchange.Redis.CommandFlags>()))
            .ReturnsAsync(new StackExchange.Redis.RedisValue());

        _mockRedisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>()))
            .ReturnsAsync(mockDatabase.Object);

        mockDatabase.Setup(x => x.StringSetAsync(It.IsAny<StackExchange.Redis.RedisKey>(), It.IsAny<StackExchange.Redis.RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<StackExchange.Redis.When>(), It.IsAny<StackExchange.Redis.CommandFlags>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.GetCachedRegimeAsync();

        // Assert
        result.Should().BeNull();
    }

    [Fact]
    public async Task GetCachedRegimeAsync_WithValidCache_ReturnsRegime()
    {
        // Arrange
        var mockDatabase = new Mock<StackExchange.Redis.IDatabase>();
        mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<StackExchange.Redis.RedisKey>(), It.IsAny<StackExchange.Redis.CommandFlags>()))
            .ReturnsAsync(new StackExchange.Redis.RedisValue("TrendingUp"));

        _mockRedisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>()))
            .ReturnsAsync(mockDatabase.Object);

        mockDatabase.Setup(x => x.StringSetAsync(It.IsAny<StackExchange.Redis.RedisKey>(), It.IsAny<StackExchange.Redis.RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<StackExchange.Redis.When>(), It.IsAny<StackExchange.Redis.CommandFlags>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.GetCachedRegimeAsync();

        // Assert
        result.Should().Be(MarketRegime.TrendingUp);
    }

    [Fact]
    public async Task GetModelVersionAsync_ReturnsVersion()
    {
        // Arrange
        var mockDatabase = new Mock<StackExchange.Redis.IDatabase>();
        mockDatabase.Setup(x => x.StringGetAsync(It.IsAny<StackExchange.Redis.RedisKey>(), It.IsAny<StackExchange.Redis.CommandFlags>()))
            .ReturnsAsync(new StackExchange.Redis.RedisValue("1234567890"));

        _mockRedisService.Setup(x => x.GetDatabaseAsync(It.IsAny<int>()))
            .ReturnsAsync(mockDatabase.Object);

        mockDatabase.Setup(x => x.StringSetAsync(It.IsAny<StackExchange.Redis.RedisKey>(), It.IsAny<StackExchange.Redis.RedisValue>(), It.IsAny<TimeSpan?>(), It.IsAny<StackExchange.Redis.When>(), It.IsAny<StackExchange.Redis.CommandFlags>()))
            .ReturnsAsync(true);

        // Act
        var result = await _service.GetModelVersionAsync();

        // Assert
        result.Should().Be(1234567890L);
    }

    [Fact]
    public async Task DetectTodayAsync_WithBreadthServiceError_UsesDefaultBreadthScore()
    {
        // Arrange
        var spxBars = new List<IndexBar>
        {
            new IndexBar(DateTime.UtcNow.AddDays(-1), 4000m, 4100m, 3950m, 4050m, 1000000),
            new IndexBar(DateTime.UtcNow, 4050m, 4150m, 4000m, 4100m, 1200000)
        };

        var vixBars = new List<IndexBar>
        {
            new IndexBar(DateTime.UtcNow.AddDays(-1), 20m, 22m, 19m, 21m, 500000),
            new IndexBar(DateTime.UtcNow, 21m, 23m, 20m, 19m, 600000)
        };

        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "I:SPX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(spxBars);
        _mockMarketDataService.Setup(x => x.GetIndexBarsAsync(It.Is<string>(s => s == "I:VIX"), It.IsAny<DateTime>(), It.IsAny<DateTime>()))
            .ReturnsAsync(vixBars);

        _mockBreadthService.Setup(x => x.GetCachedBreadthAsync())
            .ThrowsAsync(new Exception("Breadth service error"));
        _mockBreadthService.Setup(x => x.CalculateMarketBreadthAsync(It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("Breadth calculation error"));

        // Act
        var result = await _service.DetectTodayAsync();

        // Assert
        result.Should().BeOneOf(MarketRegime.Sideways, MarketRegime.TrendingUp, MarketRegime.TrendingDown, MarketRegime.Panic);
    }

    [Theory]
    [InlineData(-0.04f, 25f, -0.1f, MarketRegime.Panic)] // SPX_Ret < -0.03
    [InlineData(0.01f, 35f, 0.05f, MarketRegime.Panic)] // VIX_Level >= 30
    [InlineData(-0.02f, 20f, 0.05f, MarketRegime.TrendingDown)] // SPX_Ret < -0.012 AND VIX_Change > 0
    [InlineData(0.02f, 20f, -0.05f, MarketRegime.TrendingUp)] // SPX_Ret > 0.012 AND VIX_Change < 0
    [InlineData(0.005f, 20f, 0.01f, MarketRegime.Sideways)] // Default case
    public void CalculateRegimeLabel_WithVariousInputs_ReturnsExpectedRegime(
        float spxRet, double vixLevel, float vixChange, MarketRegime expectedRegime)
    {
        // This tests the labeling logic that would be used in training data generation
        // We can't directly test the private method, but we can test the logic
        
        MarketRegime actualRegime;
        
        if (vixLevel >= 30 || spxRet < -0.03f)
            actualRegime = MarketRegime.Panic;
        else if (spxRet < -0.012f && vixChange > 0)
            actualRegime = MarketRegime.TrendingDown;
        else if (spxRet > 0.012f && vixChange < 0)
            actualRegime = MarketRegime.TrendingUp;
        else
            actualRegime = MarketRegime.Sideways;

        // Assert
        actualRegime.Should().Be(expectedRegime);
    }

    public void Dispose()
    {
        _service?.Dispose();
    }
}
